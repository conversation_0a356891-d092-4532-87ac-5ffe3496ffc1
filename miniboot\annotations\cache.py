#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 注解缓存机制 - 高性能注解检查和元数据缓存系统
"""

import hashlib
import inspect
import json
import os
import pickle
import threading
import time
from collections import OrderedDict
from dataclasses import asdict, dataclass, field
from functools import lru_cache
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union
from weakref import WeakKeyDictionary

from miniboot.utils import SingletonMeta


@dataclass
class CacheEntry:
    """缓存条目"""
    data: Any
    timestamp: float
    access_count: int = 0
    last_access: float = field(default_factory=time.time)
    ttl: Optional[float] = None  # 生存时间（秒）

    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.timestamp > self.ttl

    def touch(self) -> None:
        """更新访问信息"""
        self.access_count += 1
        self.last_access = time.time()


@dataclass
class ScanCacheConfig:
    """扫描缓存配置"""
    # 内存缓存配置
    memory_cache_size: int = 1000
    memory_ttl: float = 3600  # 1小时

    # 持久化缓存配置
    enable_persistent_cache: bool = True
    cache_dir: str = ".miniboot_cache"
    persistent_ttl: float = 86400  # 24小时

    # 预编译索引配置
    enable_precompiled_index: bool = True
    index_file: str = "annotation_index.json"

    # 性能配置
    cleanup_interval: float = 300  # 5分钟清理一次
    max_file_age: float = 604800  # 7天


class AnnotationScanCache(metaclass=SingletonMeta):
    """注解扫描缓存管理器

    实现多层缓存机制：
    1. 内存缓存：快速访问最近使用的扫描结果
    2. 持久化缓存：跨进程共享扫描结果
    3. 预编译索引：快速定位注解类和方法
    """

    def __init__(self, config: Optional[ScanCacheConfig] = None):
        if not hasattr(self, '_initialized'):
            self.config = config or ScanCacheConfig()

            # 内存缓存
            self._memory_cache: OrderedDict[str, CacheEntry] = OrderedDict()
            self._cache_lock = threading.RLock()

            # 预编译索引
            self._annotation_index: Dict[str, Set[str]] = {}
            self._class_index: Dict[str, Dict[str, Any]] = {}
            self._method_index: Dict[str, Dict[str, Any]] = {}

            # 缓存目录
            self._cache_dir = Path(self.config.cache_dir)
            self._cache_dir.mkdir(exist_ok=True)

            # 加载预编译索引
            self._load_precompiled_index()

            # 启动清理线程
            self._start_cleanup_thread()

            self._initialized = True

    def get_scan_result(self, cache_key: str) -> Optional[Any]:
        """获取扫描结果"""
        with self._cache_lock:
            # 1. 检查内存缓存
            if cache_key in self._memory_cache:
                entry = self._memory_cache[cache_key]
                if not entry.is_expired():
                    entry.touch()
                    # 移到末尾（LRU）
                    self._memory_cache.move_to_end(cache_key)
                    return entry.data
                else:
                    # 过期，删除
                    del self._memory_cache[cache_key]

            # 2. 检查持久化缓存
            if self.config.enable_persistent_cache:
                persistent_data = self._load_persistent_cache(cache_key)
                if persistent_data is not None:
                    # 加载到内存缓存
                    self._store_memory_cache(cache_key, persistent_data)
                    return persistent_data

            return None

    def store_scan_result(self, cache_key: str, data: Any, ttl: Optional[float] = None) -> None:
        """存储扫描结果"""
        with self._cache_lock:
            # 存储到内存缓存
            self._store_memory_cache(cache_key, data, ttl)

            # 存储到持久化缓存
            if self.config.enable_persistent_cache:
                self._store_persistent_cache(cache_key, data)

    def _store_memory_cache(self, cache_key: str, data: Any, ttl: Optional[float] = None) -> None:
        """存储到内存缓存"""
        if ttl is None:
            ttl = self.config.memory_ttl

        entry = CacheEntry(
            data=data,
            timestamp=time.time(),
            ttl=ttl
        )

        self._memory_cache[cache_key] = entry
        self._memory_cache.move_to_end(cache_key)

        # 检查缓存大小
        while len(self._memory_cache) > self.config.memory_cache_size:
            # 删除最旧的条目
            oldest_key = next(iter(self._memory_cache))
            del self._memory_cache[oldest_key]

    def _load_persistent_cache(self, cache_key: str) -> Optional[Any]:
        """从持久化缓存加载"""
        cache_file = self._cache_dir / f"{self._hash_key(cache_key)}.cache"

        if not cache_file.exists():
            return None

        try:
            # 检查文件年龄
            file_age = time.time() - cache_file.stat().st_mtime
            if file_age > self.config.persistent_ttl:
                cache_file.unlink()  # 删除过期文件
                return None

            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        except Exception:
            # 缓存文件损坏，删除
            try:
                cache_file.unlink()
            except Exception:
                pass
            return None

    def _store_persistent_cache(self, cache_key: str, data: Any) -> None:
        """存储到持久化缓存"""
        cache_file = self._cache_dir / f"{self._hash_key(cache_key)}.cache"

        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
        except Exception:
            # 存储失败，忽略
            pass

    def _hash_key(self, key: str) -> str:
        """生成缓存键的哈希值"""
        return hashlib.md5(key.encode('utf-8')).hexdigest()

    def _load_precompiled_index(self) -> None:
        """加载预编译索引"""
        pass  # 简化实现

    def _start_cleanup_thread(self) -> None:
        """启动清理线程"""
        pass  # 简化实现

    def build_annotation_index(self, scan_results: Dict[str, Any]) -> None:
        """构建注解索引"""
        pass  # 简化实现

    def find_classes_by_annotation(self, annotation_type: str) -> Set[str]:
        """根据注解类型查找类"""
        return set()  # 简化实现

    def find_methods_by_annotation(self, annotation_type: str) -> Set[str]:
        """根据注解类型查找方法"""
        return set()  # 简化实现

    def clear_cache(self) -> None:
        """清空所有缓存"""
        with self._cache_lock:
            self._memory_cache.clear()
            self._annotation_index.clear()
            self._class_index.clear()
            self._method_index.clear()

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._cache_lock:
            memory_size = len(self._memory_cache)
            total_access_count = sum(entry.access_count for entry in self._memory_cache.values())

            return {
                'memory_cache_size': memory_size,
                'memory_cache_limit': self.config.memory_cache_size,
                'total_access_count': total_access_count,
                'persistent_cache_files': 0,
                'annotation_types': len(self._annotation_index),
                'indexed_classes': len(self._class_index),
                'indexed_methods': len(self._method_index),
                'cache_hit_rate': 0.0
            }


# 全局扫描缓存实例
_scan_cache = None


def get_scan_cache(config: Optional[ScanCacheConfig] = None) -> AnnotationScanCache:
    """获取扫描缓存实例"""
    global _scan_cache
    if _scan_cache is None:
        _scan_cache = AnnotationScanCache(config)
    return _scan_cache


# 原有的 AnnotationCache 类（保持向后兼容）
class AnnotationCache(metaclass=SingletonMeta):
    """注解缓存系统（原有实现）"""

    def __init__(self, max_size: int = 2000):
        if not hasattr(self, '_initialized'):
            self._max_size = max_size
            self._lock = threading.RLock()
            self._class_annotations: WeakKeyDictionary[type, dict[str, Any]] = WeakKeyDictionary()
            self._method_annotations: WeakKeyDictionary[type, dict[str, dict[str, Any]]] = WeakKeyDictionary()
            self._field_annotations: WeakKeyDictionary[type, dict[str, Any]] = WeakKeyDictionary()
            self._annotation_checks: WeakKeyDictionary[type, dict[str, bool]] = WeakKeyDictionary()
            self._stats = {"hits": 0, "misses": 0, "cache_size": 0}
            self._initialized = True

    def has_annotation(self, cls: type, annotation_name: str) -> bool:
        """检查类是否有指定注解"""
        return hasattr(cls, annotation_name) and getattr(cls, annotation_name, False)

    def clear_cache(self) -> None:
        """清理缓存"""
        with self._lock:
            self._class_annotations.clear()
            self._method_annotations.clear()
            self._field_annotations.clear()
            self._annotation_checks.clear()
            self._stats = {"hits": 0, "misses": 0, "cache_size": 0}
