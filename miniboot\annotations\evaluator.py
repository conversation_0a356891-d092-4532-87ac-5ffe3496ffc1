#!/usr/bin/env python
"""
* @author: cz
* @description: 条件评估器

实现条件装配的评估逻辑,用于在运行时判断条件是否满足.
支持各种条件类型的评估,包括属性条件、Bean条件、类条件等.
"""

import importlib
from pathlib import Path
from typing import Any, Union

from .conditional import get_conditional_metadata, is_conditional
from .metadata import ConditionalMetadata


class ConditionContext:
    """条件评估上下文

    提供条件评估所需的环境信息,包括配置属性、已注册的Bean等.
    """

    def __init__(self):
        self.properties: dict[str, Any] = {}
        self.registered_beans: set[str] = set()
        self.registered_types: set[type] = set()
        self.is_web_environment: bool = False

    def set_property(self, key: str, value: Any) -> None:
        """设置配置属性"""
        self.properties[key] = value

    def get_property(self, key: str, default: Any = None) -> Any:
        """获取配置属性"""
        return self.properties.get(key, default)

    def has_property(self, key: str) -> bool:
        """检查配置属性是否存在"""
        return key in self.properties

    def register_bean(self, name: str, bean_type: type) -> None:
        """注册Bean"""
        self.registered_beans.add(name)
        self.registered_types.add(bean_type)

    def has_bean(self, name: str) -> bool:
        """检查Bean是否存在"""
        return name in self.registered_beans

    def has_bean_type(self, bean_type: type) -> bool:
        """检查Bean类型是否存在"""
        return bean_type in self.registered_types

    def set_web_environment(self, is_web: bool) -> None:
        """设置是否为Web环境"""
        self.is_web_environment = is_web


class ConditionEvaluator:
    """条件评估器

    负责评估各种条件装配注解的条件是否满足.
    """

    def __init__(self, context: ConditionContext):
        self.context = context

    def evaluate(self, target: Union[type, callable]) -> bool:
        """评估目标是否满足条件

        Args:
            target: 要评估的类或方法

        Returns:
            如果满足条件返回True,否则返回False
        """
        if not is_conditional(target):
            return True

        metadata = get_conditional_metadata(target)
        if not metadata:
            return True

        return self._evaluate_metadata(metadata, target)

    def _evaluate_metadata(self, metadata: ConditionalMetadata, target: Union[type, callable]) -> bool:
        """根据元数据评估条件

        Args:
            metadata: 条件元数据
            target: 目标对象

        Returns:
            评估结果
        """
        condition_type = metadata.condition_type

        if condition_type == "OnProperty":
            return self._evaluate_property_condition(metadata)
        elif condition_type == "OnBean":
            return self._evaluate_bean_condition(metadata)
        elif condition_type == "OnMissingBean":
            return self._evaluate_missing_bean_condition(metadata)
        elif condition_type == "OnClass":
            return self._evaluate_class_condition(metadata)
        elif condition_type == "OnMissingClass":
            return self._evaluate_missing_class_condition(metadata)
        elif condition_type == "OnWeb":
            return self._evaluate_web_condition()
        elif condition_type == "OnExpression":
            return self._evaluate_expression_condition(metadata)
        elif condition_type == "OnResource":
            return self._evaluate_resource_condition(metadata)
        elif condition_type == "Custom":
            return self._evaluate_custom_condition(target)
        else:
            # 未知条件类型,默认返回True
            return True

    def _evaluate_property_condition(self, metadata: ConditionalMetadata) -> bool:
        """评估属性条件"""
        # 如果有前缀,检查以该前缀开头的属性
        if metadata.prefix:
            return self._evaluate_prefix_properties(metadata)

        # 如果没有属性列表,返回True
        if not metadata.properties:
            return True

        # 检查所有指定的属性
        for property_name in metadata.properties:
            if not self.context.has_property(property_name):
                if not metadata.match_if_missing:
                    return False
                continue

            if metadata.having_value is not None:
                actual_value = str(self.context.get_property(property_name))
                expected_value = metadata.having_value
                if actual_value != expected_value:
                    return False

        return True

    def _evaluate_prefix_properties(self, metadata: ConditionalMetadata) -> bool:
        """评估前缀属性条件"""
        prefix = metadata.prefix
        found_properties = []

        # 查找以前缀开头的属性
        for key in self.context.properties:
            if key.startswith(prefix):
                found_properties.append(key)

        if not found_properties:
            return metadata.match_if_missing

        # 如果有期望值,检查所有匹配的属性
        if metadata.having_value is not None:
            for prop_key in found_properties:
                actual_value = str(self.context.get_property(prop_key))
                if actual_value == metadata.having_value:
                    return True
            return False

        # 只要有匹配的属性就返回True
        return True

    def _evaluate_bean_condition(self, metadata: ConditionalMetadata) -> bool:
        """评估Bean存在条件"""
        # 检查Bean名称
        for bean_name in metadata.bean_names:
            if not self.context.has_bean(bean_name):
                return False

        # 检查Bean类型
        for class_name in metadata.class_names:
            # 这里简化处理,实际应该根据类名查找对应的类型
            found = False
            for registered_type in self.context.registered_types:
                if registered_type.__name__ == class_name:
                    found = True
                    break
            if not found:
                return False

        return True

    def _evaluate_missing_bean_condition(self, metadata: ConditionalMetadata) -> bool:
        """评估Bean不存在条件"""
        # 检查Bean名称
        for bean_name in metadata.bean_names:
            if self.context.has_bean(bean_name):
                return False

        # 检查Bean类型
        for class_name in metadata.class_names:
            # 这里简化处理,实际应该根据类名查找对应的类型
            for registered_type in self.context.registered_types:
                if registered_type.__name__ == class_name:
                    return False

        return True

    def _evaluate_class_condition(self, metadata: ConditionalMetadata) -> bool:
        """评估类存在条件"""
        for class_name in metadata.class_names:
            try:
                # 尝试导入类
                module_name, class_name_only = class_name.rsplit(".", 1)
                module = importlib.import_module(module_name)
                getattr(module, class_name_only)
            except (ImportError, AttributeError, ValueError):
                return False

        return True

    def _evaluate_web_condition(self) -> bool:
        """评估Web环境条件"""
        return self.context.is_web_environment

    def _evaluate_custom_condition(self, target: Union[type, callable]) -> bool:
        """评估自定义条件"""
        condition_class = getattr(target, "__condition_class__", None)
        if not condition_class:
            return True

        try:
            condition_instance = condition_class()
            if hasattr(condition_instance, "matches"):
                return condition_instance.matches(self.context)
        except Exception:
            # 条件评估失败,默认返回False
            return False

        return True

    def _evaluate_missing_class_condition(self, metadata: ConditionalMetadata) -> bool:
        """评估类不存在条件"""
        for class_name in metadata.class_names:
            try:
                # 尝试导入类
                module_name, class_name_only = class_name.rsplit(".", 1)
                module = importlib.import_module(module_name)
                getattr(module, class_name_only)
                # 如果能成功导入,说明类存在,条件不满足
                return False
            except (ImportError, AttributeError, ValueError):
                # 导入失败,说明类不存在,继续检查下一个
                continue

        # 所有类都不存在,条件满足
        return True

    def _evaluate_expression_condition(self, metadata: ConditionalMetadata) -> bool:
        """评估表达式条件"""
        if not metadata.expression:
            return True

        try:
            # 创建表达式评估环境
            eval_env = self._create_expression_environment()

            # 处理表达式中的属性占位符
            processed_expression = self._process_expression_placeholders(metadata.expression)

            # 评估表达式
            result = eval(processed_expression, {"__builtins__": {}}, eval_env)
            return bool(result)
        except Exception:
            # 表达式评估失败,返回False
            # 在调试模式下可以打印错误信息
            # print(f"Expression evaluation failed: {e}, expression: {metadata.expression}")
            return False

    def _evaluate_resource_condition(self, metadata: ConditionalMetadata) -> bool:
        """评估资源存在条件"""
        return all(Path(resource_path).exists() for resource_path in metadata.resources)

    def _create_expression_environment(self) -> dict:
        """创建表达式评估环境"""
        return {
            "int": int,
            "float": float,
            "str": str,
            "bool": bool,
            "len": len,
            "properties": self.context.properties,
            "has_bean": self.context.has_bean,
            "is_web": self.context.is_web_environment,
        }

    def _process_expression_placeholders(self, expression: str) -> str:
        """处理表达式中的属性占位符"""
        import re

        # 匹配 ${property.name:default_value} 格式的占位符
        pattern = r"\$\{([^}:]+)(?::([^}]*))?\}"

        def replace_placeholder(match):
            prop_name = match.group(1)
            default_value = match.group(2) if match.group(2) is not None else ""

            if self.context.has_property(prop_name):
                value = self.context.get_property(prop_name)
                # 直接返回值,不添加引号(表达式中已经有引号了)
                return str(value)
            else:
                # 使用默认值
                return default_value if default_value else ""

        return re.sub(pattern, replace_placeholder, expression)


# 便利函数
def create_condition_context(**properties) -> ConditionContext:
    """创建条件上下文

    Args:
        **properties: 初始配置属性

    Returns:
        条件上下文实例
    """
    context = ConditionContext()
    for key, value in properties.items():
        context.set_property(key, value)
    return context


def evaluate_condition(target: Union[type, callable], context: ConditionContext) -> bool:
    """评估条件

    Args:
        target: 要评估的目标
        context: 条件上下文

    Returns:
        评估结果
    """
    evaluator = ConditionEvaluator(context)
    return evaluator.evaluate(target)


# ==================== 自动配置顺序管理器 ====================

class AutoConfigurationOrderManager:
    """自动配置顺序管理器

    负责管理自动配置类的执行顺序,支持依赖关系和优先级排序.
    """

    def __init__(self):
        self._configurations: list[type] = []
        self._dependency_graph: dict[type, set[type]] = {}

    def add_configuration(self, config_class: type) -> None:
        """添加自动配置类

        Args:
            config_class: 自动配置类
        """
        if config_class not in self._configurations:
            self._configurations.append(config_class)
            self._build_dependency_graph(config_class)

    def add_configurations(self, config_classes: list[type]) -> None:
        """批量添加自动配置类

        Args:
            config_classes: 自动配置类列表
        """
        for config_class in config_classes:
            self.add_configuration(config_class)

    def get_ordered_configurations(self) -> list[type]:
        """获取按依赖关系排序的配置类列表

        Returns:
            排序后的配置类列表

        Raises:
            ValueError: 如果存在循环依赖
        """
        # 首先按优先级排序
        priority_sorted = self._sort_by_priority()

        # 然后进行拓扑排序处理依赖关系
        return self._topological_sort(priority_sorted)

    def _build_dependency_graph(self, config_class: type) -> None:
        """构建依赖关系图

        Args:
            config_class: 配置类
        """
        from .conditional import (
            get_auto_configure_after,
            get_auto_configure_before,
            has_auto_configure_order
        )

        if not has_auto_configure_order(config_class):
            return

        # 处理 @AutoConfigureAfter 依赖
        after_classes = get_auto_configure_after(config_class)
        if after_classes:
            if config_class not in self._dependency_graph:
                self._dependency_graph[config_class] = set()
            self._dependency_graph[config_class].update(after_classes)

        # 处理 @AutoConfigureBefore 依赖
        before_classes = get_auto_configure_before(config_class)
        for before_class in before_classes:
            if before_class not in self._dependency_graph:
                self._dependency_graph[before_class] = set()
            self._dependency_graph[before_class].add(config_class)

    def _sort_by_priority(self) -> list[type]:
        """按优先级排序配置类

        Returns:
            按优先级排序的配置类列表
        """
        from .conditional import get_auto_configure_order

        return sorted(
            self._configurations,
            key=lambda cls: get_auto_configure_order(cls)
        )

    def _topological_sort(self, configurations: list[type]) -> list[type]:
        """拓扑排序处理依赖关系

        Args:
            configurations: 配置类列表

        Returns:
            拓扑排序后的配置类列表

        Raises:
            ValueError: 如果存在循环依赖
        """
        # 构建入度表
        in_degree = {cls: 0 for cls in configurations}
        graph = {cls: [] for cls in configurations}

        # 构建图和计算入度
        for cls in configurations:
            if cls in self._dependency_graph:
                for dependency in self._dependency_graph[cls]:
                    if dependency in configurations:
                        graph[dependency].append(cls)
                        in_degree[cls] += 1

        # Kahn算法进行拓扑排序，保持优先级顺序
        from .conditional import get_auto_configure_order

        # 初始队列按优先级排序
        initial_queue = [cls for cls in configurations if in_degree[cls] == 0]
        initial_queue.sort(key=lambda cls: get_auto_configure_order(cls))

        queue = initial_queue
        result = []

        while queue:
            current = queue.pop(0)
            result.append(current)

            # 收集可以加入队列的邻居
            ready_neighbors = []
            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    ready_neighbors.append(neighbor)

            # 按优先级排序新加入的邻居
            ready_neighbors.sort(key=lambda cls: get_auto_configure_order(cls))

            # 将排序后的邻居插入队列，保持整体优先级顺序
            queue.extend(ready_neighbors)
            queue.sort(key=lambda cls: get_auto_configure_order(cls))

        # 检查是否存在循环依赖
        if len(result) != len(configurations):
            remaining = [cls for cls in configurations if cls not in result]
            raise ValueError(f"Circular dependency detected in auto-configurations: {remaining}")

        return result

    def validate_dependencies(self) -> bool:
        """验证依赖关系是否有效

        Returns:
            如果依赖关系有效返回True,否则返回False
        """
        try:
            self.get_ordered_configurations()
            return True
        except ValueError:
            return False


def create_auto_configuration_order_manager(config_classes: list[type]) -> AutoConfigurationOrderManager:
    """创建自动配置顺序管理器

    Args:
        config_classes: 自动配置类列表

    Returns:
        自动配置顺序管理器实例
    """
    manager = AutoConfigurationOrderManager()
    manager.add_configurations(config_classes)
    return manager
