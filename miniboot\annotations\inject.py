#!/usr/bin/env python
"""
* @author: cz
* @description: 依赖注入注解实现

实现Mini-Boot框架的依赖注入注解,包括@Autowired、@Inject、@Qualifier等.
这些注解用于标记需要自动装配的字段和方法,支持类型推断和名称指定.
"""

import inspect
from typing import Any, Callable, Optional, Union

from .metadata import (AutowiredMetadata, DependsOnMetadata, LazyMetadata,
                       PrimaryMetadata, QualifierMetadata)


def Autowired(  # noqa: N802
    target: Optional[Union[Callable, type]] = None, *, required: bool = True, name: Optional[str] = None
) -> Union[Callable, type, Callable[[Union[Callable, type]], Union[Callable, type]]]:
    """自动装配注解装饰器

    用于标记需要自动装配的字段、方法参数或setter方法.
    支持字段注入和setter方法注入两种方式.

    Args:
        target: 被装饰的字段、方法或类
        required: 是否必需,默认为True
        name: 指定Bean名称,默认根据类型推断

    Returns:
        装饰后的字段、方法或装饰器函数

    Examples:
        # 字段注入
        class UserService:
            @Autowired
            user_repository: UserRepository

        # setter方法注入
        class UserService:
            @Autowired
            def set_user_repository(self, user_repository: UserRepository):
                self.user_repository = user_repository

        # 指定Bean名称
        class UserService:
            @Autowired(name="primaryUserRepository")
            user_repository: UserRepository
    """

    def decorator(target_obj: Union[Callable, type]) -> Union[Callable, type]:
        # 检查是否有pending的qualifier信息
        pending_qualifier = getattr(target_obj, "__qualifier_pending__", None)

        # 创建自动装配元数据
        metadata = AutowiredMetadata(required=required, name=name, qualifier=pending_qualifier)

        # 清除pending标记
        if hasattr(target_obj, "__qualifier_pending__"):
            delattr(target_obj, "__qualifier_pending__")

        # 判断装饰目标类型
        if inspect.isfunction(target_obj) or inspect.ismethod(target_obj):
            # 方法装饰
            target_obj.__autowired_metadata__ = metadata
            target_obj.__is_autowired__ = True
            target_obj.__autowired_required__ = required
            target_obj.__autowired_name__ = name

            # 标记为setter方法
            target_obj.__is_setter_injection__ = True

        elif hasattr(target_obj, "__annotations__"):
            # 类装饰 - 处理字段注入
            if not hasattr(target_obj, "__autowired_fields__"):
                target_obj.__autowired_fields__ = {}

            # 获取类型注解
            annotations = getattr(target_obj, "__annotations__", {})
            for field_name, _field_type in annotations.items():
                # 为每个字段创建装配元数据
                field_metadata = AutowiredMetadata(required=required, name=name or field_name)
                target_obj.__autowired_fields__[field_name] = field_metadata

        else:
            # 字段装饰(通过描述符)
            # 这种情况下target_obj是字段本身
            target_obj.__autowired_metadata__ = metadata
            target_obj.__is_autowired__ = True
            target_obj.__autowired_required__ = required
            target_obj.__autowired_name__ = name

            # 标记为字段注入
            target_obj.__is_field_injection__ = True

        return target_obj

    # 如果直接传入目标对象,不带参数(@Autowired)
    if target is not None:
        return decorator(target)

    # 带参数调用(@Autowired(...))
    return decorator


def Inject(  # noqa: N802
    target: Optional[Union[Callable, type]] = None, *, name: Optional[str] = None
) -> Union[Callable, type, Callable[[Union[Callable, type]], Union[Callable, type]]]:
    """JSR-330标准依赖注入注解装饰器

    与@Autowired类似,但遵循JSR-330标准.默认required=True.

    Args:
        target: 被装饰的字段、方法或类
        name: 指定Bean名称,默认根据类型推断

    Returns:
        装饰后的字段、方法或装饰器函数

    Examples:
        class UserService:
            @Inject
            user_repository: UserRepository

        class UserService:
            @Inject(name="primaryUserRepository")
            user_repository: UserRepository
    """

    def decorator(target_obj: Union[Callable, type]) -> Union[Callable, type]:
        # 使用Autowired实现,但标记为JSR-330
        result = Autowired(target_obj, required=True, name=name)

        # 添加JSR-330标记
        if hasattr(result, "__autowired_metadata__") or hasattr(result, "__autowired_fields__"):
            result.__is_jsr330_inject__ = True

        return result

    if target is not None:
        return decorator(target)

    return decorator


def Qualifier(  # noqa: N802
    value: str,
) -> Callable[[Union[Callable, type]], Union[Callable, type]]:
    """限定符注解装饰器

    用于指定具体的Bean名称进行注入,解决同类型多个Bean的歧义问题.

    Args:
        value: 限定符值,通常是Bean名称

    Returns:
        装饰器函数

    Examples:
        class UserService:
            @Autowired
            @Qualifier("primaryUserRepository")
            user_repository: UserRepository

        class UserService:
            @Autowired
            @Qualifier("cacheUserRepository")
            cache_repository: UserRepository
    """

    def decorator(target: Union[Callable, type]) -> Union[Callable, type]:
        # 创建限定符元数据
        metadata = QualifierMetadata(value=value)

        # 存储元数据
        target.__qualifier_metadata__ = metadata
        target.__is_qualified__ = True
        target.__qualifier_value__ = value

        # 如果已经有Autowired元数据,更新qualifier信息
        if hasattr(target, "__autowired_metadata__"):
            # 直接修改现有元数据对象的qualifier属性
            target.__autowired_metadata__.qualifier = value
        elif hasattr(target, "__autowired_fields__"):
            # 为所有字段添加qualifier
            for field_metadata in target.__autowired_fields__.values():
                field_metadata.qualifier = value
        else:
            # 如果还没有Autowired元数据,设置pending标记
            target.__qualifier_pending__ = value

        return target

    return decorator


def Primary(  # noqa: N802
    target: Union[Callable, type],
) -> Union[Callable, type]:
    """主要Bean注解装饰器

    标记一个Bean为主要Bean,当存在多个同类型Bean时优先注入.

    Args:
        target: 被装饰的类或方法

    Returns:
        装饰后的类或方法

    Examples:
        @Component
        @Primary
        class PrimaryUserRepository(UserRepository):
            pass

        @Configuration
        class DatabaseConfig:
            @Bean
            @Primary
            def primary_data_source(self):
                return DataSource("primary")
    """
    # 创建主要Bean元数据
    metadata = PrimaryMetadata()

    # 存储元数据
    target.__primary_metadata__ = metadata
    target.__is_primary__ = True
    target.__primary__ = True

    # 标记需要更新组件元数据
    target.__primary_pending__ = True

    return target


def Lazy(  # noqa: N802
    target: Optional[Union[Callable, type]] = None, *, value: bool = True
) -> Union[Callable, type, Callable[[Union[Callable, type]], Union[Callable, type]]]:
    """延迟初始化注解装饰器

    标记一个Bean为延迟初始化,只有在第一次使用时才创建实例.

    Args:
        target: 被装饰的类或方法
        value: 是否延迟初始化,默认为True

    Returns:
        装饰后的类、方法或装饰器函数

    Examples:
        @Component
        @Lazy
        class ExpensiveService:
            pass

        @Component
        @Lazy(value=False)  # 显式禁用延迟初始化
        class EagerService:
            pass
    """

    def decorator(target_obj: Union[Callable, type]) -> Union[Callable, type]:
        # 创建延迟初始化元数据
        metadata = LazyMetadata(value=value)

        # 存储元数据
        target_obj.__lazy_metadata__ = metadata
        target_obj.__is_lazy__ = True
        target_obj.__lazy_value__ = value

        # 标记需要更新组件元数据
        target_obj.__lazy_pending__ = value

        return target_obj

    if target is not None:
        return decorator(target)

    return decorator


def DependsOn(  # noqa: N802
    *bean_names: str,
) -> Callable[[Union[Callable, type]], Union[Callable, type]]:
    """依赖关系注解装饰器

    声明Bean的依赖关系,确保依赖的Bean先于当前Bean初始化.

    Args:
        *bean_names: 依赖的Bean名称列表

    Returns:
        装饰器函数

    Examples:
        @Component
        @DependsOn("databaseService", "cacheService")
        class UserService:
            pass

        @Configuration
        class AppConfig:
            @Bean
            @DependsOn("dataSource")
            def user_repository(self):
                return UserRepository()
    """

    def decorator(target: Union[Callable, type]) -> Union[Callable, type]:
        # 创建依赖关系元数据
        metadata = DependsOnMetadata(bean_names=list(bean_names))

        # 存储元数据
        target.__depends_on_metadata__ = metadata
        target.__is_depends_on__ = True
        target.__depends_on_beans__ = list(bean_names)

        # 标记需要更新组件元数据
        target.__depends_on_pending__ = list(bean_names)

        return target

    return decorator


# 便捷函数:检查对象是否有特定注解
def is_autowired(obj: Any) -> bool:
    """检查对象是否有@Autowired注解(使用缓存优化)"""
    # 对于类对象,使用缓存
    if inspect.isclass(obj):
        from .cache import AnnotationCache
        cache = AnnotationCache()
        return cache.has_annotation(obj, "__is_autowired__")
    # 对于方法/函数,直接检查
    return hasattr(obj, "__is_autowired__") and obj.__is_autowired__


def is_qualified(obj: Any) -> bool:
    """检查对象是否有@Qualifier注解(使用缓存优化)"""
    # 对于类对象,使用缓存
    if inspect.isclass(obj):
        from .cache import AnnotationCache
        cache = AnnotationCache()
        return cache.has_annotation(obj, "__is_qualified__")
    # 对于方法/函数,直接检查
    return hasattr(obj, "__is_qualified__") and obj.__is_qualified__


def is_primary(obj: Any) -> bool:
    """检查对象是否有@Primary注解(使用缓存优化)"""
    # 对于类对象,使用缓存
    if inspect.isclass(obj):
        from .cache import AnnotationCache
        cache = AnnotationCache()
        return cache.has_annotation(obj, "__is_primary__")
    # 对于方法/函数,直接检查
    return hasattr(obj, "__is_primary__") and obj.__is_primary__


def is_lazy(obj: Any) -> bool:
    """检查对象是否有@Lazy注解(使用缓存优化)"""
    # 对于类对象,使用缓存
    if inspect.isclass(obj):
        from .cache import AnnotationCache
        cache = AnnotationCache()
        return cache.has_annotation(obj, "__is_lazy__")
    # 对于方法/函数,直接检查
    return hasattr(obj, "__is_lazy__") and obj.__is_lazy__


def autowired_metadata(obj: Any) -> Optional[AutowiredMetadata]:
    """获取对象的@Autowired元数据"""
    return getattr(obj, "__autowired_metadata__", None)


def qualifier_value(obj: Any) -> Optional[str]:
    """获取对象的@Qualifier值"""
    return getattr(obj, "__qualifier_value__", None)


def depends_on_beans(obj: Any) -> list[str]:
    """获取对象依赖的Bean名称列表"""
    return getattr(obj, "__depends_on_beans__", [])
