#!/usr/bin/env python
"""
* @author: cz
* @description: 注解元数据类定义

定义各种注解的元数据结构,用于存储注解参数和配置信息.
这些元数据类将被注解装饰器使用,存储在被装饰的类或方法上.
"""

import threading
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Optional, Union


class Scope(Enum):
    """核心Bean作用域枚举

    只包含框架核心的作用域类型，Web相关作用域在web模块中定义。
    """

    SINGLETON = "singleton"  # 单例模式
    PROTOTYPE = "prototype"  # 原型模式

    @classmethod
    def from_string(cls, value: str) -> "Scope":
        """从字符串创建 Scope

        Args:
            value: 作用域字符串值

        Returns:
            Scope: 对应的作用域枚举

        Raises:
            ValueError: 如果值无效
        """
        value = value.lower().strip()
        for scope in cls:
            if scope.value == value:
                return scope
        raise ValueError(f"Invalid scope: {value}. Valid values are: {[s.value for s in cls]}")


@dataclass
class ComponentMetadata:
    """组件注解元数据"""

    name: Optional[str] = None  # Bean名称,默认为类名首字母小写
    scope: Scope = Scope.SINGLETON  # Bean作用域,默认为单例
    lazy: bool = False  # 是否延迟初始化
    primary: bool = False  # 是否为主要Bean
    depends_on: list[str] = field(default_factory=list)  # 依赖的Bean名称列表


@dataclass
class BeanMetadata:
    """Bean方法注解元数据"""

    name: Optional[str] = None  # Bean名称,默认为方法名
    scope: Scope = Scope.SINGLETON  # Bean作用域
    init_method: Optional[str] = None  # 初始化方法名
    destroy_method: Optional[str] = None  # 销毁方法名
    depends_on: list[str] = field(default_factory=list)  # 依赖的Bean名称列表


@dataclass
class AutowiredMetadata:
    """自动装配注解元数据"""

    required: bool = True  # 是否必需,默认为True
    name: Optional[str] = None  # 指定Bean名称,默认根据类型推断
    qualifier: Optional[str] = None  # 限定符,用于区分同类型的多个Bean


@dataclass
class ValueMetadata:
    """配置值注解元数据"""

    value: str  # 配置键或表达式
    required: bool = True  # 是否必需
    default_value: Optional[Any] = None  # 默认值


@dataclass
class ConfigurationPropertiesMetadata:
    """配置属性注解元数据"""

    prefix: str = ""  # 配置前缀
    ignore_unknown_fields: bool = True  # 是否忽略未知字段
    ignore_invalid_fields: bool = False  # 是否忽略无效字段
    validate: bool = True  # 是否进行配置验证


@dataclass
class ConditionalMetadata:
    """条件装配注解元数据"""

    condition_type: str  # 条件类型:OnProperty, OnBean, OnMissingBean, OnClass, OnMissingClass, OnWeb, OnExpression, OnResource
    properties: list[str] = field(default_factory=list)  # 属性名列表
    having_value: Optional[str] = None  # 期望的属性值
    match_if_missing: bool = False  # 属性不存在时是否匹配
    bean_names: list[str] = field(default_factory=list)  # Bean名称列表
    class_names: list[str] = field(default_factory=list)  # 类名列表
    prefix: Optional[str] = None  # 属性前缀
    expression: Optional[str] = None  # 条件表达式
    java_range: Optional[str] = None  # Java版本范围
    resources: list[str] = field(default_factory=list)  # 资源路径列表


@dataclass
class ProfileMetadata:
    """Profile 注解元数据"""

    profiles: list[str] = field(default_factory=list)  # Profile 名称列表
    expression: Optional[str] = None  # Profile 表达式


@dataclass
class ScopeMetadata:
    """作用域注解元数据"""

    scope: Scope = Scope.SINGLETON  # 作用域类型
    proxy_mode: str = "default"  # 代理模式


@dataclass
class ComponentScanMetadata:
    """组件扫描注解元数据"""

    base_packages: list[str] = field(default_factory=list)  # 扫描的基础包路径
    include_filters: list[str] = field(default_factory=list)  # 包含过滤器
    exclude_filters: list[str] = field(default_factory=list)  # 排除过滤器
    lazy_init: bool = False  # 是否延迟初始化


@dataclass
class PostConstructMetadata:
    """初始化方法注解元数据"""

    method: str  # 方法名


@dataclass
class PreDestroyMetadata:
    """销毁方法注解元数据"""

    method: str  # 方法名


@dataclass
class AsyncMetadata:
    """异步方法注解元数据"""

    pool: Optional[str] = None  # 线程池名称
    method: str = ""  # 方法名


@dataclass
class ScheduledMetadata:
    """调度方法注解元数据"""

    cron: Optional[str] = None  # cron表达式
    fixed_rate: Optional[str] = None  # 固定频率
    fixed_delay: Optional[str] = None  # 固定延迟
    initial_delay: Optional[str] = None  # 初始延迟
    zone: Optional[str] = None  # 时区
    concurrent: bool = False  # 是否允许并发执行


@dataclass
class EventListenerMetadata:
    """事件监听器注解元数据"""

    method: str  # 方法名称
    event_type: Optional[type] = None  # 事件类型
    condition: Optional[str] = None  # 执行条件表达式
    order: int = 0  # 执行顺序
    async_exec: bool = False  # 是否异步执行


@dataclass
class QualifierMetadata:
    """限定符注解元数据"""

    value: str  # 限定符值


@dataclass
class PrimaryMetadata:
    """主要Bean注解元数据"""

    pass  # 标记性注解,无需额外属性


@dataclass
class LazyMetadata:
    """延迟初始化注解元数据"""

    value: bool = True  # 是否延迟初始化


@dataclass
class DependsOnMetadata:
    """依赖关系注解元数据"""

    bean_names: list[str] = field(default_factory=list)  # 依赖的Bean名称列表


@dataclass
class OrderMetadata:
    """顺序注解元数据"""

    value: int = 0  # 顺序值,数值越小优先级越高


@dataclass
class EnableAutoConfigurationMetadata:
    """自动配置注解元数据"""

    exclude: list[str] = field(default_factory=list)  # 排除的自动配置类


@dataclass
class ImportMetadata:
    """导入注解元数据"""

    import_classes: list[type] = field(default_factory=list)  # 要导入的配置类列表


# 元数据类型映射,用于快速查找
METADATA_TYPES = {
    "component": ComponentMetadata,
    "bean": BeanMetadata,
    "autowired": AutowiredMetadata,
    "value": ValueMetadata,
    "configuration_properties": ConfigurationPropertiesMetadata,
    "conditional": ConditionalMetadata,
    "component_scan": ComponentScanMetadata,
    "post_construct": PostConstructMetadata,
    "pre_destroy": PreDestroyMetadata,
    "async": AsyncMetadata,
    "scheduled": ScheduledMetadata,
    "event_listener": EventListenerMetadata,
    "qualifier": QualifierMetadata,
    "primary": PrimaryMetadata,
    "lazy": LazyMetadata,
    "depends_on": DependsOnMetadata,
    "order": OrderMetadata,
    "import": ImportMetadata,
    # 添加类名映射,用于序列化
    "ComponentMetadata": ComponentMetadata,
    "BeanMetadata": BeanMetadata,
    "AutowiredMetadata": AutowiredMetadata,
    "ValueMetadata": ValueMetadata,
    "ConfigurationPropertiesMetadata": ConfigurationPropertiesMetadata,
    "ConditionalMetadata": ConditionalMetadata,
    "ComponentScanMetadata": ComponentScanMetadata,
    "PostConstructMetadata": PostConstructMetadata,
    "PreDestroyMetadata": PreDestroyMetadata,
    "AsyncMetadata": AsyncMetadata,
    "ScheduledMetadata": ScheduledMetadata,
    "EventListenerMetadata": EventListenerMetadata,
    "QualifierMetadata": QualifierMetadata,
    "PrimaryMetadata": PrimaryMetadata,
    "LazyMetadata": LazyMetadata,
    "DependsOnMetadata": DependsOnMetadata,
    "OrderMetadata": OrderMetadata,
    "ImportMetadata": ImportMetadata,
}


def get_metadata_type(annotation_type: str) -> type:
    """获取注解类型对应的元数据类

    Args:
        annotation_type: 注解类型名称

    Returns:
        对应的元数据类

    Raises:
        KeyError: 如果注解类型不存在
    """
    if annotation_type not in METADATA_TYPES:
        raise KeyError(f"Unknown annotation type: {annotation_type}")
    return METADATA_TYPES[annotation_type]


def create_metadata(annotation_type: str, **kwargs) -> Any:
    """创建注解元数据实例

    Args:
        annotation_type: 注解类型名称
        **kwargs: 元数据参数

    Returns:
        元数据实例
    """
    metadata_class = get_metadata_type(annotation_type)
    return metadata_class(**kwargs)


# 注解元数据管理器
import json
import pickle
from dataclasses import asdict, is_dataclass

from miniboot.utils import SingletonMeta


class MetadataRegistry(metaclass=SingletonMeta):
    """元数据注册表

    管理所有已注册的元数据信息,提供统一的存储和查询接口.
    优化内存使用和查询性能.使用单例模式确保全局唯一性.
    """

    def __init__(self, enable_weak_refs: bool = False, max_entries: int = 10000):
        # 防止重复初始化
        if not hasattr(self, "_initialized"):
            # 按类型存储元数据
            self._metadata_by_type: dict[str, dict[str, Any]] = {}

            # 按对象存储元数据(支持类和方法)
            self._metadata_by_object: dict[int, dict[str, Any]] = {}

            # 配置选项
            self._enable_weak_refs = enable_weak_refs
            self._max_entries = max_entries

            # 统计信息
            self._stats = {"total_registered": 0, "total_queries": 0, "cache_hits": 0, "cache_misses": 0}

            # 线程安全锁
            self._lock = threading.RLock()

            self._initialized = True

    def register_metadata(self, target: Union[type, Any], metadata_type: str, metadata: Any) -> None:
        """注册元数据

        Args:
            target: 目标对象(类或方法)
            metadata_type: 元数据类型
            metadata: 元数据实例
        """
        with self._lock:
            target_id = id(target)

            # 按类型存储
            if metadata_type not in self._metadata_by_type:
                self._metadata_by_type[metadata_type] = {}

            target_key = f"{target.__module__}.{target.__name__}" if hasattr(target, "__name__") else str(target_id)
            self._metadata_by_type[metadata_type][target_key] = metadata

            # 按对象存储
            if target_id not in self._metadata_by_object:
                self._metadata_by_object[target_id] = {}

            self._metadata_by_object[target_id][metadata_type] = metadata

            # 更新统计
            self._stats["total_registered"] += 1

            # 检查是否需要清理
            if len(self._metadata_by_object) > self._max_entries:
                self._cleanup_old_entries()

    def get_metadata(self, target: Union[type, Any], metadata_type: str) -> Optional[Any]:
        """获取元数据

        Args:
            target: 目标对象
            metadata_type: 元数据类型

        Returns:
            元数据实例,如果不存在则返回None
        """
        with self._lock:
            self._stats["total_queries"] += 1

            target_id = id(target)

            # 首先尝试从对象缓存获取
            if target_id in self._metadata_by_object:
                metadata = self._metadata_by_object[target_id].get(metadata_type)
                if metadata is not None:
                    self._stats["cache_hits"] += 1
                    return metadata

            # 尝试从类型缓存获取
            if metadata_type in self._metadata_by_type:
                target_key = f"{target.__module__}.{target.__name__}" if hasattr(target, "__name__") else str(target_id)
                metadata = self._metadata_by_type[metadata_type].get(target_key)
                if metadata is not None:
                    self._stats["cache_hits"] += 1
                    return metadata

            self._stats["cache_misses"] += 1
            return None

    def get_all_metadata_by_type(self, metadata_type: str) -> dict[str, Any]:
        """获取指定类型的所有元数据

        Args:
            metadata_type: 元数据类型

        Returns:
            元数据字典
        """
        with self._lock:
            return self._metadata_by_type.get(metadata_type, {}).copy()

    def get_all_metadata_for_target(self, target: Union[type, Any]) -> dict[str, Any]:
        """获取目标对象的所有元数据

        Args:
            target: 目标对象

        Returns:
            元数据字典
        """
        target_id = id(target)
        return self._metadata_by_object.get(target_id, {}).copy()

    def has_metadata(self, target: Union[type, Any], metadata_type: str) -> bool:
        """检查是否存在指定的元数据

        Args:
            target: 目标对象
            metadata_type: 元数据类型

        Returns:
            如果存在返回True,否则返回False
        """
        return self.get_metadata(target, metadata_type) is not None

    def remove_metadata(self, target: Union[type, Any], metadata_type: Optional[str] = None) -> bool:
        """移除元数据

        Args:
            target: 目标对象
            metadata_type: 元数据类型,如果为None则移除所有元数据

        Returns:
            如果成功移除返回True,否则返回False
        """
        with self._lock:
            target_id = id(target)
            removed = False

            if metadata_type is None:
                # 移除所有元数据
                if target_id in self._metadata_by_object:
                    del self._metadata_by_object[target_id]
                    removed = True

                # 从类型缓存中移除
                target_key = f"{target.__module__}.{target.__name__}" if hasattr(target, "__name__") else str(target_id)
                for type_dict in self._metadata_by_type.values():
                    if target_key in type_dict:
                        del type_dict[target_key]
                        removed = True
            else:
                # 移除指定类型的元数据
                if target_id in self._metadata_by_object and metadata_type in self._metadata_by_object[target_id]:
                    del self._metadata_by_object[target_id][metadata_type]
                    removed = True

                    # 如果没有其他元数据,移除整个条目
                    if not self._metadata_by_object[target_id]:
                        del self._metadata_by_object[target_id]

                # 从类型缓存中移除
                if metadata_type in self._metadata_by_type:
                    target_key = f"{target.__module__}.{target.__name__}" if hasattr(target, "__name__") else str(target_id)
                    if target_key in self._metadata_by_type[metadata_type]:
                        del self._metadata_by_type[metadata_type][target_key]
                        removed = True

            return removed

    def clear_all(self) -> None:
        """清除所有元数据"""
        with self._lock:
            self._metadata_by_type.clear()
            self._metadata_by_object.clear()
            self._stats = {"total_registered": 0, "total_queries": 0, "cache_hits": 0, "cache_misses": 0}

    def get_stats(self) -> dict[str, Any]:
        """获取统计信息

        Returns:
            统计信息字典
        """
        with self._lock:
            stats = self._stats.copy()
            stats.update(
                {
                    "total_types": len(self._metadata_by_type),
                    "total_objects": len(self._metadata_by_object),
                    "hit_rate": self._stats["cache_hits"] / max(1, self._stats["total_queries"]) * 100,
                }
            )
            return stats

    def _cleanup_old_entries(self) -> None:
        """清理旧的条目(改进的LRU策略)"""
        if len(self._metadata_by_object) <= self._max_entries * 0.8:
            return

        # 计算需要清理的条目数量
        items_to_remove = len(self._metadata_by_object) - int(self._max_entries * 0.7)
        keys_to_remove = list(self._metadata_by_object.keys())[:items_to_remove]

        # 清理对象缓存和对应的类型缓存
        for object_id in keys_to_remove:
            # 获取要删除的元数据
            metadata_dict = self._metadata_by_object.get(object_id, {})

            # 从类型缓存中移除对应条目
            for metadata_type, metadata in metadata_dict.items():
                if metadata_type in self._metadata_by_type:
                    # 查找并移除对应的类型缓存条目
                    type_dict = self._metadata_by_type[metadata_type]
                    keys_to_remove_from_type = []
                    for target_key, cached_metadata in type_dict.items():
                        if cached_metadata is metadata:  # 使用对象身份比较
                            keys_to_remove_from_type.append(target_key)

                    # 移除找到的条目
                    for target_key in keys_to_remove_from_type:
                        del type_dict[target_key]

                    # 如果类型字典为空，移除整个类型
                    if not type_dict:
                        del self._metadata_by_type[metadata_type]

            # 从对象缓存中移除
            del self._metadata_by_object[object_id]


class MetadataValidator:
    """元数据验证器"""

    @staticmethod
    def validate_metadata(metadata: Any) -> list[str]:
        """验证元数据

        Args:
            metadata: 元数据对象

        Returns:
            错误信息列表,空列表表示验证通过
        """
        errors = []

        if not is_dataclass(metadata):
            errors.append("Metadata must be a dataclass")
            return errors

        # 验证ComponentMetadata
        if isinstance(metadata, ComponentMetadata):
            if metadata.name is not None and not isinstance(metadata.name, str):
                errors.append("Component name must be a string")
            if not isinstance(metadata.scope, Scope):
                errors.append("Component scope must be a Scope enum")

        # 验证BeanMetadata
        elif isinstance(metadata, BeanMetadata):
            if metadata.name is not None and not isinstance(metadata.name, str):
                errors.append("Bean name must be a string")
            if not isinstance(metadata.scope, Scope):
                errors.append("Bean scope must be a Scope enum")

        # 验证AutowiredMetadata
        elif isinstance(metadata, AutowiredMetadata):
            if not isinstance(metadata.required, bool):
                errors.append("Autowired required must be a boolean")

        # 验证ValueMetadata
        elif isinstance(metadata, ValueMetadata):
            if not isinstance(metadata.value, str):
                errors.append("Value expression must be a string")
            if not isinstance(metadata.required, bool):
                errors.append("Value required must be a boolean")

        # 验证ConfigurationPropertiesMetadata
        elif isinstance(metadata, ConfigurationPropertiesMetadata):
            if not isinstance(metadata.prefix, str):
                errors.append("Configuration properties prefix must be a string")

        return errors


class MetadataSerializer:
    """元数据序列化器"""

    @staticmethod
    def to_dict(metadata: Any) -> dict[str, Any]:
        """将元数据转换为字典

        Args:
            metadata: 元数据对象

        Returns:
            字典表示
        """
        if not is_dataclass(metadata):
            raise ValueError("Metadata must be a dataclass")

        result = asdict(metadata)
        result["__type__"] = metadata.__class__.__name__
        return result

    @staticmethod
    def from_dict(data: dict[str, Any]) -> Any:
        """从字典创建元数据对象

        Args:
            data: 字典数据

        Returns:
            元数据对象
        """
        if "__type__" not in data:
            raise ValueError("Dictionary must contain '__type__' field")

        type_name = data.pop("__type__")
        metadata_class = get_metadata_type(type_name)
        return metadata_class(**data)

    @staticmethod
    def to_json(metadata: Any) -> str:
        """将元数据序列化为JSON

        Args:
            metadata: 元数据对象

        Returns:
            JSON字符串
        """
        return json.dumps(MetadataSerializer.to_dict(metadata), indent=2)

    @staticmethod
    def from_json(json_str: str) -> Any:
        """从JSON反序列化元数据

        Args:
            json_str: JSON字符串

        Returns:
            元数据对象
        """
        data = json.loads(json_str)
        return MetadataSerializer.from_dict(data)

    @staticmethod
    def to_pickle(metadata: Any) -> bytes:
        """将元数据序列化为pickle格式

        Args:
            metadata: 元数据对象

        Returns:
            pickle字节数据
        """
        return pickle.dumps(metadata)

    @staticmethod
    def from_pickle(pickle_data: bytes) -> Any:
        """从pickle反序列化元数据

        Args:
            pickle_data: pickle字节数据

        Returns:
            元数据对象
        """
        return pickle.loads(pickle_data)


# 全局便捷函数
def get_global_registry() -> MetadataRegistry:
    """获取全局元数据注册表(单例)

    Returns:
        全局注册表实例
    """
    return MetadataRegistry()


def register_metadata(target: Union[type, Any], annotation_type: str, metadata: Any):
    """注册元数据到全局注册表

    Args:
        target: 目标对象(类或方法)
        annotation_type: 注解类型
        metadata: 元数据对象
    """
    registry = get_global_registry()
    registry.register_metadata(target, annotation_type, metadata)


def get_metadata(target: Union[type, Any], annotation_type: str) -> Optional[Any]:
    """从全局注册表获取元数据

    Args:
        target: 目标对象
        annotation_type: 注解类型

    Returns:
        元数据对象,如果不存在则返回None
    """
    registry = get_global_registry()
    return registry.get_metadata(target, annotation_type)


def validate_metadata(metadata: Any) -> list[str]:
    """验证元数据

    Args:
        metadata: 元数据对象

    Returns:
        错误信息列表
    """
    return MetadataValidator.validate_metadata(metadata)


def serialize_metadata(metadata: Any, format: str = "dict") -> Union[dict, str, bytes]:
    """序列化元数据

    Args:
        metadata: 元数据对象
        format: 序列化格式 ('dict', 'json', 'pickle')

    Returns:
        序列化后的数据
    """
    if format == "dict":
        return MetadataSerializer.to_dict(metadata)
    elif format == "json":
        return MetadataSerializer.to_json(metadata)
    elif format == "pickle":
        return MetadataSerializer.to_pickle(metadata)
    else:
        raise ValueError(f"Unsupported format: {format}")


def deserialize_metadata(data: Union[dict, str, bytes], format: str = "dict") -> Any:
    """反序列化元数据

    Args:
        data: 序列化数据
        format: 数据格式 ('dict', 'json', 'pickle')

    Returns:
        元数据对象
    """
    if format == "dict":
        return MetadataSerializer.from_dict(data)
    elif format == "json":
        return MetadataSerializer.from_json(data)
    elif format == "pickle":
        return MetadataSerializer.from_pickle(data)
    else:
        raise ValueError(f"Unsupported format: {format}")
