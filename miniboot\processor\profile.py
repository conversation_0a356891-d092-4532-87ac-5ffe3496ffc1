#!/usr/bin/env python
"""
* @author: cz
* @description: Profile 处理器实现

实现 Profile Bean 后置处理器,用于处理 @Profile 注解的条件化 Bean 创建.
支持多环境配置和复杂的 Profile 表达式评估.
"""

from typing import Any

from loguru import logger

from ..annotations.profile import (ProfileEvaluator, get_profile_metadata,
                                   is_profile)
from ..errors import BeanCreationError
from ..errors import ProcessorExecutionError as BeanProcessingError
from .base import BeanPostProcessor, ProcessorOrder


class ProfileBeanPostProcessor(BeanPostProcessor):
    """Profile Bean 后置处理器

    负责处理 @Profile 注解,根据当前激活的 Profile 决定是否创建 Bean.
    """

    def __init__(self, environment=None):
        """初始化 Profile 处理器

        Args:
            environment: 环境配置实例,用于获取激活的 Profile
        """
        self._environment = environment
        self._evaluator = ProfileEvaluator(environment)
        self._processed_beans: set[str] = set()

    def set_environment(self, environment) -> None:
        """设置环境配置

        Args:
            environment: 环境配置实例
        """
        self._environment = environment
        self._evaluator = ProfileEvaluator(environment)

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """在 Bean 初始化前处理 @Profile 注解

        检查 Bean 类是否有 @Profile 注解,如果有则评估 Profile 条件.
        如果条件不满足,则阻止 Bean 的创建.

        Args:
            bean: Bean 实例
            bean_name: Bean 名称

        Returns:
            处理后的 Bean 实例

        Raises:
            BeanProcessingError: 当 Profile 条件不满足时
        """
        if bean is None or bean_name in self._processed_beans:
            return bean

        try:
            # 检查 Bean 类是否有 @Profile 注解
            bean_class = bean.__class__
            if not is_profile(bean_class):
                return bean

            # 评估 Profile 条件
            if not self._evaluator.evaluate(bean_class):
                # Profile 条件不满足,记录日志并阻止 Bean 创建
                metadata = get_profile_metadata(bean_class)
                profile_info = self._get_profile_info(metadata)

                active_profiles = self._evaluator.get_active_profiles()
                logger.debug(f"Bean '{bean_name}' skipped due to Profile condition: {profile_info}, active profiles: {active_profiles}")

                # 抛出异常阻止 Bean 创建
                raise BeanCreationError(f"Bean '{bean_name}' creation skipped due to Profile condition: {profile_info}", bean_name)

            # Profile 条件满足,允许 Bean 创建
            metadata = get_profile_metadata(bean_class)
            profile_info = self._get_profile_info(metadata)
            active_profiles = self._evaluator.get_active_profiles()

            logger.debug(f"Bean '{bean_name}' Profile condition satisfied: {profile_info}, active profiles: {active_profiles}")

            # 标记为已处理
            self._processed_beans.add(bean_name)

            return bean

        except BeanCreationError:
            # 重新抛出 Bean 创建错误
            raise
        except Exception as e:
            raise BeanProcessingError(
                f"Failed to process @Profile annotation for bean '{bean_name}'", bean_name=bean_name, processor_name=self.__class__.__name__, cause=e
            ) from e

    def post_process_after_initialization(self, bean: Any, _bean_name: str) -> Any:
        """在 Bean 初始化后处理(Profile 处理器不需要后处理)

        Args:
            bean: Bean 实例
            bean_name: Bean 名称

        Returns:
            原始 Bean 实例
        """
        return bean

    def get_order(self) -> int:
        """获取处理器执行顺序

        Profile 处理器需要在其他处理器之前执行,
        确保只有满足 Profile 条件的 Bean 才会被创建和处理.

        Returns:
            执行顺序(数字越小优先级越高)
        """
        return ProcessorOrder.VALIDATION_PROCESSOR - 10  # 在验证处理器之前执行

    def supports(self, bean: Any, _bean_name: str) -> bool:
        """检查是否支持处理指定的 Bean

        Args:
            bean: Bean 实例
            bean_name: Bean 名称

        Returns:
            True 表示支持处理此 Bean
        """
        if bean is None:
            return False

        # 检查 Bean 类是否有 @Profile 注解
        return is_profile(bean.__class__)

    def _get_profile_info(self, metadata) -> str:
        """获取 Profile 信息字符串

        Args:
            metadata: Profile 元数据

        Returns:
            Profile 信息字符串
        """
        if not metadata:
            return "no profile"

        if metadata.expression:
            return f"expression='{metadata.expression}'"
        elif metadata.profiles:
            return f"profiles={metadata.profiles}"
        else:
            return "no profile"

    def get_active_profiles(self) -> list[str]:
        """获取当前激活的 Profile 列表

        Returns:
            激活的 Profile 名称列表
        """
        return self._evaluator.get_active_profiles()

    def get_default_profiles(self) -> list[str]:
        """获取默认的 Profile 列表

        Returns:
            默认的 Profile 名称列表
        """
        return self._evaluator.get_default_profiles()

    def accepts_profiles(self, *profiles: str) -> bool:
        """检查环境是否接受指定的 Profile

        Args:
            *profiles: Profile 名称列表

        Returns:
            如果环境接受任一指定 Profile 返回 True
        """
        return self._evaluator.accepts_profiles(*profiles)


class ProfileConditionEvaluator:
    """Profile 条件评估器

    提供独立的 Profile 条件评估功能,可以在 Bean 创建之前进行条件检查.
    """

    def __init__(self, environment=None):
        """初始化 Profile 条件评估器

        Args:
            environment: 环境配置实例
        """
        self.environment = environment
        self.evaluator = ProfileEvaluator(environment)

    def should_create_bean(self, bean_class: type) -> bool:
        """检查是否应该创建指定类的 Bean

        Args:
            bean_class: Bean 类

        Returns:
            True 表示应该创建 Bean
        """
        if not is_profile(bean_class):
            return True

        return self.evaluator.evaluate(bean_class)

    def filter_bean_classes(self, bean_classes: list[type]) -> list[type]:
        """过滤 Bean 类列表,只保留满足 Profile 条件的类

        Args:
            bean_classes: Bean 类列表

        Returns:
            过滤后的 Bean 类列表
        """
        filtered_classes = []

        for bean_class in bean_classes:
            if self.should_create_bean(bean_class):
                filtered_classes.append(bean_class)
            else:
                # 记录被过滤的 Bean
                metadata = get_profile_metadata(bean_class)
                profile_info = self._get_profile_info(metadata)
                active_profiles = self.evaluator.get_active_profiles()

                logger.debug(
                    f"Bean class '{bean_class.__name__}' filtered out due to Profile condition: {profile_info}, active profiles: {active_profiles}"
                )

        return filtered_classes

    def _get_profile_info(self, metadata) -> str:
        """获取 Profile 信息字符串

        Args:
            metadata: Profile 元数据

        Returns:
            Profile 信息字符串
        """
        if not metadata:
            return "no profile"

        if metadata.expression:
            return f"expression='{metadata.expression}'"
        elif metadata.profiles:
            return f"profiles={metadata.profiles}"
        else:
            return "no profile"


# 便利函数
def create_profile_processor(environment=None) -> ProfileBeanPostProcessor:
    """创建 Profile Bean 后置处理器

    Args:
        environment: 环境配置实例

    Returns:
        Profile Bean 后置处理器实例
    """
    return ProfileBeanPostProcessor(environment)


def create_profile_condition_evaluator(environment=None) -> ProfileConditionEvaluator:
    """创建 Profile 条件评估器

    Args:
        environment: 环境配置实例

    Returns:
        Profile 条件评估器实例
    """
    return ProfileConditionEvaluator(environment)
