#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
异步调度模块指标采集自动配置

实现异步调度模块的指标采集功能，包括：
- 任务执行统计：任务总数、成功率、失败率
- 线程池状态：活跃线程数、队列长度、完成任务数
- 调度延迟监控：任务调度延迟、执行延迟分析
- APScheduler 集成：作业状态、触发器统计
- 异步任务性能：async/await 任务执行时间分布

配置条件：
- starters.actuator.metrics.core-modules.scheduler=true (默认启用)
- 依赖 ActuatorStarterAutoConfiguration 已配置

使用示例：
    # application.yml
    starters:
        actuator:
            metrics:
                core-modules:
                    scheduler: true  # 启用调度模块指标采集
"""

import threading
import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union

from loguru import logger

from miniboot.annotations.conditional import (ConditionalOnBean,
                                              ConditionalOnProperty)
from miniboot.annotations.core import Bean, Configuration
from miniboot.autoconfigure.base import AutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
from miniboot.monitoring.interfaces import MetricsCollector, MetricsData


@dataclass
class SchedulerMetrics:
    """调度模块指标数据"""

    # 任务执行统计
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    running_tasks: int = 0
    success_rate: float = 0.0

    # 执行时间统计
    total_execution_time: float = 0.0
    avg_execution_time: float = 0.0
    min_execution_time: float = float('inf')
    max_execution_time: float = 0.0

    # 线程池状态
    active_threads: int = 0
    queue_length: int = 0
    pool_size: int = 0
    max_pool_size: int = 0

    # 调度延迟监控
    avg_schedule_delay: float = 0.0
    max_schedule_delay: float = 0.0
    total_schedule_delays: int = 0

    # APScheduler 集成统计
    active_jobs: int = 0
    pending_jobs: int = 0
    paused_jobs: int = 0

    # 异步任务性能
    async_tasks: int = 0
    sync_tasks: int = 0
    async_avg_time: float = 0.0
    sync_avg_time: float = 0.0

    # 错误统计
    timeout_tasks: int = 0
    retry_tasks: int = 0
    consecutive_failures: int = 0

    def calculate_derived_metrics(self):
        """计算派生指标"""
        # 计算成功率
        if self.total_tasks > 0:
            self.success_rate = self.completed_tasks / self.total_tasks

        # 计算平均执行时间
        completed_count = self.completed_tasks + self.failed_tasks
        if completed_count > 0:
            self.avg_execution_time = self.total_execution_time / completed_count

        # 计算平均调度延迟
        if self.total_schedule_delays > 0:
            # avg_schedule_delay 已在收集时计算
            pass


class SchedulerMetricsCollector(MetricsCollector):
    """调度模块指标采集器

    负责收集调度器的各种性能指标和统计信息。
    通过监控任务执行、线程池状态、调度延迟等过程，
    提供详细的调度性能分析数据。
    """

    def __init__(self):
        """初始化调度指标采集器"""
        self._metrics = SchedulerMetrics()
        self._lock = threading.RLock()
        self._start_time = time.time()

        # 监控的调度器实例
        self._monitored_schedulers: List[Any] = []

        # 监控的线程池管理器
        self._monitored_thread_pools: List[Any] = []

        # 监控的异步执行器
        self._monitored_async_executors: List[Any] = []

        # 监控的任务管理器
        self._monitored_task_managers: List[Any] = []

        logger.info("SchedulerMetricsCollector initialized")

    def register_scheduler(self, scheduler: Any) -> None:
        """注册要监控的调度器

        Args:
            scheduler: 调度器实例 (MiniBootScheduler 或 TaskScheduler)
        """
        with self._lock:
            if scheduler not in self._monitored_schedulers:
                self._monitored_schedulers.append(scheduler)
                logger.debug(f"Registered scheduler for monitoring: {scheduler}")

    def register_thread_pool_manager(self, pool_manager: Any) -> None:
        """注册要监控的线程池管理器

        Args:
            pool_manager: 线程池管理器实例
        """
        with self._lock:
            if pool_manager not in self._monitored_thread_pools:
                self._monitored_thread_pools.append(pool_manager)
                logger.debug(f"Registered thread pool manager for monitoring: {pool_manager}")

    def register_async_executor(self, executor: Any) -> None:
        """注册要监控的异步执行器

        Args:
            executor: 异步执行器实例
        """
        with self._lock:
            if executor not in self._monitored_async_executors:
                self._monitored_async_executors.append(executor)
                logger.debug(f"Registered async executor for monitoring: {executor}")

    def register_task_manager(self, task_manager: Any) -> None:
        """注册要监控的任务管理器

        Args:
            task_manager: 任务管理器实例
        """
        with self._lock:
            if task_manager not in self._monitored_task_managers:
                self._monitored_task_managers.append(task_manager)
                logger.debug(f"Registered task manager for monitoring: {task_manager}")

    async def collect_metrics_async(self) -> SchedulerMetrics:
        """收集当前的调度模块指标

        Returns:
            SchedulerMetrics: 当前的指标数据
        """
        import asyncio

        # 使用同步锁保护的内部函数
        def _collect_with_lock():
            with self._lock:
                # 重置指标
                metrics = SchedulerMetrics()

                # 收集调度器指标
                for scheduler in self._monitored_schedulers:
                    self._collect_scheduler_metrics(scheduler, metrics)

                # 收集线程池指标
                for pool_manager in self._monitored_thread_pools:
                    self._collect_thread_pool_metrics(pool_manager, metrics)

                # 收集异步执行器指标
                for executor in self._monitored_async_executors:
                    self._collect_async_executor_metrics(executor, metrics)

                # 收集任务管理器指标
                for task_manager in self._monitored_task_managers:
                    self._collect_task_manager_metrics(task_manager, metrics)

                # 计算派生指标
                metrics.calculate_derived_metrics()

                # 更新内部指标
                self._metrics = metrics

                logger.debug(f"Collected scheduler metrics: {metrics}")
                return metrics

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _collect_with_lock)

    def _collect_scheduler_metrics(self, scheduler: Any, metrics: SchedulerMetrics) -> None:
        """收集调度器指标"""
        try:
            # 尝试获取调度器状态
            if hasattr(scheduler, 'state') and scheduler.state:
                # 获取作业统计
                if hasattr(scheduler, '_scheduler') and scheduler._scheduler:
                    jobs = scheduler._scheduler.get_jobs()
                    metrics.active_jobs += len(jobs)

                # 获取任务统计
                if hasattr(scheduler, 'all_metrics'):
                    all_task_metrics = scheduler.all_metrics()
                    for task_id, task_metrics in all_task_metrics.items():
                        if hasattr(task_metrics, 'total_executions'):
                            metrics.total_tasks += task_metrics.total_executions
                            metrics.completed_tasks += task_metrics.successful_executions
                            metrics.failed_tasks += task_metrics.failed_executions
                            metrics.total_execution_time += task_metrics.total_execution_time

                            if task_metrics.min_execution_time < metrics.min_execution_time:
                                metrics.min_execution_time = task_metrics.min_execution_time
                            if task_metrics.max_execution_time > metrics.max_execution_time:
                                metrics.max_execution_time = task_metrics.max_execution_time

        except Exception as e:
            logger.debug(f"Failed to collect scheduler metrics: {e}")

    def _collect_thread_pool_metrics(self, pool_manager: Any, metrics: SchedulerMetrics) -> None:
        """收集线程池指标"""
        try:
            if hasattr(pool_manager, 'get_all_pools'):
                pools = pool_manager.get_all_pools()
                for pool_name, pool in pools.items():
                    if hasattr(pool, 'get_metrics'):
                        pool_metrics = pool.get_metrics()
                        metrics.active_threads += pool_metrics.get('active_threads', 0)
                        metrics.queue_length += pool_metrics.get('queue_size', 0)
                        metrics.pool_size += pool_metrics.get('pool_size', 0)
                        metrics.max_pool_size += pool_metrics.get('max_pool_size', 0)

        except Exception as e:
            logger.debug(f"Failed to collect thread pool metrics: {e}")

    def _collect_async_executor_metrics(self, executor: Any, metrics: SchedulerMetrics) -> None:
        """收集异步执行器指标"""
        try:
            if hasattr(executor, 'get_metrics'):
                executor_metrics = executor.get_metrics()
                metrics.async_tasks += executor_metrics.get('completed_tasks', 0)
                metrics.async_avg_time = executor_metrics.get('average_execution_time', 0.0)

        except Exception as e:
            logger.debug(f"Failed to collect async executor metrics: {e}")

    def _collect_task_manager_metrics(self, task_manager: Any, metrics: SchedulerMetrics) -> None:
        """收集任务管理器指标"""
        try:
            if hasattr(task_manager, 'all_metrics'):
                all_metrics = task_manager.all_metrics()
                for task_id, task_metrics in all_metrics.items():
                    if hasattr(task_metrics, 'consecutive_failures'):
                        metrics.consecutive_failures = max(
                            metrics.consecutive_failures,
                            task_metrics.consecutive_failures
                        )

        except Exception as e:
            logger.debug(f"Failed to collect task manager metrics: {e}")

    async def get_metrics_dict(self) -> Dict[str, Any]:
        """获取指标的字典表示

        Returns:
            Dict[str, Any]: 指标字典
        """
        metrics = await self.collect_metrics_async()
        return {
            'task_execution': {
                'total_tasks': metrics.total_tasks,
                'completed_tasks': metrics.completed_tasks,
                'failed_tasks': metrics.failed_tasks,
                'running_tasks': metrics.running_tasks,
                'success_rate': metrics.success_rate,
            },
            'execution_time': {
                'total_execution_time': metrics.total_execution_time,
                'avg_execution_time': metrics.avg_execution_time,
                'min_execution_time': metrics.min_execution_time if metrics.min_execution_time != float('inf') else 0.0,
                'max_execution_time': metrics.max_execution_time,
            },
            'thread_pool': {
                'active_threads': metrics.active_threads,
                'queue_length': metrics.queue_length,
                'pool_size': metrics.pool_size,
                'max_pool_size': metrics.max_pool_size,
            },
            'scheduling': {
                'avg_schedule_delay': metrics.avg_schedule_delay,
                'max_schedule_delay': metrics.max_schedule_delay,
                'total_schedule_delays': metrics.total_schedule_delays,
            },
            'jobs': {
                'active_jobs': metrics.active_jobs,
                'pending_jobs': metrics.pending_jobs,
                'paused_jobs': metrics.paused_jobs,
            },
            'async_performance': {
                'async_tasks': metrics.async_tasks,
                'sync_tasks': metrics.sync_tasks,
                'async_avg_time': metrics.async_avg_time,
                'sync_avg_time': metrics.sync_avg_time,
            },
            'errors': {
                'timeout_tasks': metrics.timeout_tasks,
                'retry_tasks': metrics.retry_tasks,
                'consecutive_failures': metrics.consecutive_failures,
            }
        }

    def reset_metrics(self) -> None:
        """重置所有指标"""
        with self._lock:
            self._metrics = SchedulerMetrics()
            self._start_time = time.time()
            logger.info("Scheduler metrics reset")

    # MetricsCollector 接口实现方法
    def collect_metrics(self) -> List[MetricsData]:
        """收集指标数据 - MetricsCollector 接口实现"""
        import asyncio
        import time
        try:
            loop = asyncio.get_running_loop()
            metrics = asyncio.run_coroutine_threadsafe(self.collect_metrics_async(), loop).result(timeout=5.0)
        except RuntimeError:
            metrics = asyncio.run(self.collect_metrics_async())

        # Convert SchedulerMetrics to MetricsData list
        metrics_data = []
        metrics_data.append(MetricsData(
            name="scheduler.total_tasks",
            value=metrics.total_tasks,
            unit="count",
            tags={"module": "scheduler"},
            timestamp=time.time()
        ))
        metrics_data.append(MetricsData(
            name="scheduler.completed_tasks",
            value=metrics.completed_tasks,
            unit="count",
            tags={"module": "scheduler"},
            timestamp=time.time()
        ))
        metrics_data.append(MetricsData(
            name="scheduler.failed_tasks",
            value=metrics.failed_tasks,
            unit="count",
            tags={"module": "scheduler"},
            timestamp=time.time()
        ))
        metrics_data.append(MetricsData(
            name="scheduler.running_tasks",
            value=metrics.running_tasks,
            unit="count",
            tags={"module": "scheduler"},
            timestamp=time.time()
        ))
        metrics_data.append(MetricsData(
            name="scheduler.active_threads",
            value=metrics.active_threads,
            unit="count",
            tags={"module": "scheduler"},
            timestamp=time.time()
        ))
        metrics_data.append(MetricsData(
            name="scheduler.avg_execution_time",
            value=metrics.avg_execution_time,
            unit="seconds",
            tags={"module": "scheduler"},
            timestamp=time.time()
        ))
        return metrics_data

    def get_collector_name(self) -> str:
        """获取收集器名称 - MetricsCollector 接口实现"""
        return "SchedulerMetricsCollector"

    def get_supported_metrics(self) -> List[str]:
        """获取支持的指标列表 - MetricsCollector 接口实现"""
        return [
            "scheduler.total_tasks",
            "scheduler.completed_tasks",
            "scheduler.failed_tasks",
            "scheduler.running_tasks",
            "scheduler.active_threads",
            "scheduler.avg_execution_time"
        ]

    def is_available(self) -> bool:
        """检查收集器是否可用 - MetricsCollector 接口实现"""
        # Scheduler 指标收集器总是可用的，即使没有监控特定调度器
        # 它可以提供基本的调度器统计信息
        return True


@ConditionalOnProperty(name="miniboot.starters.actuator.metrics.core-modules.scheduler", match_if_missing=True)
@ConditionalOnBean(name="actuator_context")
class SchedulerMetricsAutoConfiguration(AutoConfiguration):
    """异步调度模块指标采集自动配置类

    当满足以下条件时自动配置调度指标采集：
    1. starters.actuator.metrics.core-modules.scheduler=true (默认启用)
    2. ActuatorStarterAutoConfiguration 已配置

    注册的 Bean：
    - scheduler_metrics_collector: 调度指标采集器
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="scheduler-metrics-auto-configuration",
            description="异步调度模块指标采集自动配置",
            priority=200,  # 中等优先级
            auto_configure_after=["actuator-starter-auto-configuration"],
        )

    @Bean
    def scheduler_metrics_collector(self) -> SchedulerMetricsCollector:
        """创建调度指标采集器 Bean

        Returns:
            SchedulerMetricsCollector: 调度指标采集器实例
        """
        collector = SchedulerMetricsCollector()
        logger.debug("Created SchedulerMetricsCollector bean")
        return collector
