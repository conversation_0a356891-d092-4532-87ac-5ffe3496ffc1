"""异步装饰器工具模块

统一的异步编程装饰器：timeout, retry, cached, concurrent_limit, robust_async
"""

import asyncio
import functools
import time
from typing import Any, Callable, Optional, TypeVar, Union

from loguru import logger

F = TypeVar("F", bound=Callable[..., Any])


def timeout(timeout_seconds: float, raise_on_timeout: bool = True) -> Callable[[F], F]:
    """
    异步超时装饰器 - 统一版本

    整合了 actuator/tools.py 和 asyncs/tools.py 的实现，提供更完善的功能。

    Args:
        timeout_seconds: 超时时间(秒)
        raise_on_timeout: 超时时是否抛出异常，False则返回None

    Returns:
        装饰器函数

    Example:
        @timeout(5.0)
        async def slow_function():
            await asyncio.sleep(10)  # 会在5秒后超时

        @timeout(3.0, raise_on_timeout=False)
        async def optional_operation():
            # 超时时返回None而不抛出异常
            return await some_async_operation()
    """

    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
            except asyncio.TimeoutError as err:
                logger.warning(f"函数 {func.__name__} 执行超时 ({timeout_seconds}秒)")

                if raise_on_timeout:
                    raise asyncio.TimeoutError(f"函数 {func.__name__} 执行超时 ({timeout_seconds}秒)") from err
                else:
                    return None

        return wrapper  # type: ignore

    return decorator


def cached(ttl: float = 60.0, max_size: int = 128, key_func: Optional[Callable] = None) -> Callable[[F], F]:
    """
    异步缓存装饰器 - 增强版本

    基于 actuator/tools.py 的实现，增加了自定义键函数和更好的统计。

    Args:
        ttl: 缓存生存时间(秒)
        max_size: 最大缓存条目数
        key_func: 自定义缓存键生成函数

    Returns:
        装饰器函数

    Example:
        @cached(ttl=30.0, max_size=64)
        async def expensive_operation(param: str):
            return await some_expensive_async_call(param)

        @cached(ttl=60.0, key_func=lambda x, y: f"{x}:{y}")
        async def custom_key_operation(x, y):
            return await some_operation(x, y)
    """

    def decorator(func: F) -> F:
        cache: dict[str, tuple[Any, float]] = {}

        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 生成缓存键
            cache_key = key_func(*args, **kwargs) if key_func else f"{func.__name__}:{hash((args, tuple(sorted(kwargs.items()))))}"

            # 检查缓存
            current_time = time.time()
            if cache_key in cache:
                result, timestamp = cache[cache_key]
                if current_time - timestamp < ttl:
                    logger.debug(f"缓存命中: {func.__name__}")
                    return result
                else:
                    # 缓存过期,删除
                    del cache[cache_key]

            # 缓存未命中或已过期,执行函数
            logger.debug(f"缓存未命中: {func.__name__}")
            result = await func(*args, **kwargs)

            # 存储到缓存
            if len(cache) >= max_size:
                # 删除最旧的缓存项
                oldest_key = min(cache.keys(), key=lambda k: cache[k][1])
                del cache[oldest_key]

            cache[cache_key] = (result, current_time)
            return result

        # 添加缓存管理方法
        wrapper.cache_clear = lambda: cache.clear()
        wrapper.cache_info = lambda: {"size": len(cache), "max_size": max_size, "ttl": ttl, "keys": list(cache.keys())}

        return wrapper

    return decorator


def retry(
    max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0, exceptions: Union[type, tuple] = Exception, on_retry: Optional[Callable] = None
) -> Callable[[F], F]:
    """
    异步重试装饰器 - 增强版本

    基于 actuator/tools.py 的实现，增加了异常类型过滤和回调功能。

    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间(秒)
        backoff: 退避倍数
        exceptions: 需要重试的异常类型
        on_retry: 重试时的回调函数

    Returns:
        装饰器函数

    Example:
        @retry(max_attempts=3, delay=1.0, backoff=2.0)
        async def unreliable_operation():
            return await some_unreliable_async_call()

        @retry(max_attempts=5, exceptions=(ConnectionError, TimeoutError))
        async def network_operation():
            return await network_call()
    """

    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            current_delay = delay
            last_exception = None

            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}, {current_delay}秒后重试")

                        # 调用重试回调
                        if on_retry:
                            try:
                                await on_retry(attempt + 1, e, current_delay) if asyncio.iscoroutinefunction(on_retry) else on_retry(
                                    attempt + 1, e, current_delay
                                )
                            except Exception as callback_error:
                                logger.error(f"重试回调失败: {callback_error}")

                        await asyncio.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logger.error(f"函数 {func.__name__} 所有 {max_attempts} 次尝试都失败了")

            # 所有重试都失败,抛出最后一个异常
            raise last_exception

        return wrapper

    return decorator


def concurrent_limit(max_concurrent: int = 10) -> Callable[[F], F]:
    """
    异步并发限制装饰器 - 来自 actuator/tools.py

    Args:
        max_concurrent: 最大并发数

    Returns:
        装饰器函数

    Example:
        @concurrent_limit(max_concurrent=5)
        async def limited_operation():
            return await some_async_operation()
    """
    semaphore = asyncio.Semaphore(max_concurrent)

    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            async with semaphore:
                return await func(*args, **kwargs)

        return wrapper

    return decorator


# 便捷的组合装饰器
def robust_async(timeout_seconds: float = 30.0, max_attempts: int = 3, cache_ttl: float = 60.0, max_concurrent: int = 10) -> Callable[[F], F]:
    """
    健壮的异步装饰器组合

    组合了超时、重试、缓存和并发限制功能。

    Args:
        timeout_seconds: 超时时间
        max_attempts: 最大重试次数
        cache_ttl: 缓存TTL
        max_concurrent: 最大并发数

    Returns:
        装饰器函数
    """

    def decorator(func: F) -> F:
        # 应用装饰器链：并发限制 -> 缓存 -> 重试 -> 超时
        return concurrent_limit(max_concurrent)(cached(ttl=cache_ttl)(retry(max_attempts=max_attempts)(timeout(timeout_seconds)(func))))

    return decorator
